# PlayCanvas 球体移动脚本指南

## 概述

这个脚本为PlayCanvas中的球体实体添加了完整的移动和跳跃功能，使用物理引擎来实现真实的运动效果。

## 功能特性

### ✅ 已实现的功能

1. **WASD键控制移动**
   - W键：向前移动（-Z方向）
   - S键：向后移动（+Z方向）
   - A键：向左移动（-X方向）
   - D键：向右移动（+X方向）

2. **方向键支持**
   - 上下左右方向键也可以控制移动
   - 与WASD键功能相同

3. **跳跃功能**
   - 空格键触发跳跃
   - 只有在地面上才能跳跃
   - 跳跃冷却时间防止连续跳跃

4. **地面检测**
   - 使用射线检测判断是否在地面上
   - 结合垂直速度检测提高准确性
   - 防止无限跳跃

5. **物理驱动**
   - 使用RigidBody组件的applyForce()进行移动
   - 使用applyImpulse()进行跳跃
   - 真实的物理反馈和碰撞

## 脚本属性

### power (移动力度)
- **类型**: Number
- **默认值**: 500
- **描述**: 控制球体水平移动的力度
- **调节建议**: 
  - 较小值(100-300): 缓慢移动
  - 中等值(400-600): 正常移动
  - 较大值(700-1000): 快速移动

### jumpForce (跳跃力度)
- **类型**: Number
- **默认值**: 10
- **描述**: 控制球体跳跃的冲击力
- **调节建议**:
  - 较小值(5-8): 小跳跃
  - 中等值(10-15): 正常跳跃
  - 较大值(16-25): 高跳跃

## 实体设置要求

确保球体实体具有以下组件：

### 1. Render Component
```javascript
{
  type: "sphere",
  enabled: true
}
```

### 2. Collision Component
```javascript
{
  type: "sphere",
  radius: 0.5,
  enabled: true
}
```

### 3. RigidBody Component
```javascript
{
  type: "dynamic",
  mass: 1,
  friction: 0.5,
  restitution: 0.8,
  linearDamping: 0.1,
  angularDamping: 0.1,
  enabled: true
}
```

### 4. Script Component
```javascript
{
  scripts: {
    sphereMovement: {
      enabled: true,
      attributes: {
        power: 500,
        jumpForce: 10
      }
    }
  }
}
```

## 场景设置建议

### 地面设置
确保场景中有地面实体：
- Render Component (type: plane)
- Collision Component (type: box)
- RigidBody Component (type: static)

### 重力设置
确保场景物理设置中重力正确：
```javascript
gravity: [0, -9.8, 0]
```

## 控制键位

| 键位 | 功能 | 备注 |
|------|------|------|
| W | 向前移动 | -Z方向 |
| S | 向后移动 | +Z方向 |
| A | 向左移动 | -X方向 |
| D | 向右移动 | +X方向 |
| 空格 | 跳跃 | 仅在地面时有效 |
| ↑ | 向前移动 | 与W键相同 |
| ↓ | 向后移动 | 与S键相同 |
| ← | 向左移动 | 与A键相同 |
| → | 向右移动 | 与D键相同 |

## 技术实现细节

### 地面检测算法
1. **垂直速度检测**: 检查球体的垂直速度是否接近零
2. **位置稳定性**: 比较当前帧和上一帧的Y位置变化
3. **碰撞事件**: 监听碰撞开始事件，检查碰撞法线方向
4. **安全检查**: 防止球体掉出世界边界
5. **跳跃冷却**: 设置跳跃冷却时间防止连续跳跃

**修复说明**: 移除了有问题的射线检测API调用，改用更稳定的物理状态检测方法。

### 移动实现
- 使用`applyForce()`施加持续的推力
- 力的大小由`power`属性控制
- 力的方向根据按键确定
- 支持多键同时按下的组合移动

### 跳跃实现
- 使用`applyImpulse()`施加瞬间冲击力
- 冲击力大小由`jumpForce`属性控制
- 只在检测到地面时才能跳跃
- 跳跃后设置0.2秒冷却时间

## 调试和优化

### 控制台输出
脚本会在以下情况输出调试信息：
- 脚本初始化时
- 球体跳跃时
- 脚本销毁时

### 性能优化
- 地面检测每帧执行，但计算量很小
- 移动力只在有按键输入时计算和应用
- 使用对象池避免频繁创建Vec3对象

### 常见问题解决

#### 🔧 已修复的问题
- **射线检测错误**: 修复了`raycastFirst` API调用错误，改用更稳定的物理状态检测

#### 🐛 其他常见问题
1. **球体不移动**:
   - 检查RigidBody组件是否为dynamic类型
   - 确保mass > 0
   - 检查是否有碰撞体阻挡

2. **无法跳跃**:
   - 确保有地面实体且碰撞检测正常
   - 检查地面的RigidBody是否为static类型
   - 调整jumpForce值

3. **移动太快/太慢**:
   - 调整power属性值
   - 检查RigidBody的linearDamping设置

4. **跳跃太高/太低**:
   - 调整jumpForce属性值
   - 检查场景重力设置

5. **球体穿透地面**:
   - 确保地面有Collision组件
   - 检查碰撞体大小设置
   - 调整物理时间步长

## 扩展建议

可以考虑添加的功能：
- 冲刺功能（Shift键）
- 双跳功能
- 移动音效
- 粒子效果
- 摄像机跟随
- 移动轨迹显示
