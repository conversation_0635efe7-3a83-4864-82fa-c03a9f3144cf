# PlayCanvas 球体物理问题诊断

## 🔍 问题现象
- ✅ 控制台显示"球体跳跃！"日志
- ❌ 球体实际上没有移动
- ❌ 视觉上看不到任何运动效果

## 🔧 已执行的修复措施

### 1. 增加了详细的调试日志
```javascript
console.log('球体跳跃！应用冲击力:', jumpImpulse.y);
console.log('跳跃前速度:', currentVel.y.toFixed(2));
// 延迟检查跳跃后的速度
setTimeout(() => {
    var newVel = this.entity.rigidbody.linearVelocity;
    console.log('跳跃后速度:', newVel.y.toFixed(2));
}, 100);
```

### 2. 调整了物理参数
- **质量**: 从 1 增加到 5
- **线性阻尼**: 从 0.1 减少到 0.05
- **角度阻尼**: 从 0.1 减少到 0.05

### 3. 改进了跳跃逻辑
```javascript
// 先清除垂直速度，然后应用跳跃冲击力
var currentVel = this.entity.rigidbody.linearVelocity;
this.entity.rigidbody.linearVelocity = new pc.Vec3(currentVel.x, 0, currentVel.z);

// 应用跳跃冲击力
var jumpImpulse = new pc.Vec3(0, this.jumpForce, 0);
this.entity.rigidbody.applyImpulse(jumpImpulse);
```

### 4. 添加了物理测试脚本
创建了 `physicsTest` 脚本来验证物理系统是否正常工作。

## 📊 当前实体配置

### 球体实体 ("可移动球体")
```json
{
  "position": [2, 1, 0],
  "components": {
    "render": {
      "type": "sphere",
      "enabled": true
    },
    "collision": {
      "type": "sphere",
      "radius": 0.5,
      "enabled": true
    },
    "rigidbody": {
      "type": "dynamic",
      "mass": 5,
      "linearDamping": 0.05,
      "angularDamping": 0.05,
      "friction": 0.5,
      "restitution": 0.8,
      "enabled": true
    },
    "script": {
      "scripts": ["sphereMovement", "physicsTest"]
    }
  }
}
```

### 地面实体 ("Plane")
```json
{
  "position": [0, 0, 0],
  "scale": [8, 1, 8],
  "components": {
    "render": {
      "type": "plane",
      "enabled": true
    },
    "collision": {
      "type": "box",
      "halfExtents": [8, 0.1, 8],
      "enabled": true
    },
    "rigidbody": {
      "type": "static",
      "enabled": true
    }
  }
}
```

## 🎯 下一步调试步骤

### 1. 检查控制台输出
运行场景后，查看控制台是否显示：
- "=== 物理系统测试 ==="
- 实体名称和位置信息
- RigidBody类型和质量
- "应用测试冲击力..."

### 2. 观察测试冲击力效果
物理测试脚本会在1秒后自动应用一个向上的冲击力(5)，观察球体是否移动。

### 3. 检查可能的问题

#### A. 物理系统未启用
```javascript
// 检查物理系统是否正常
console.log('Physics enabled:', this.app.systems.rigidbody.enabled);
```

#### B. 时间缩放问题
```javascript
// 检查时间缩放
console.log('Time scale:', this.app.timeScale);
```

#### C. 实体层级问题
```javascript
// 检查实体是否在正确的层级
console.log('Entity enabled:', this.entity.enabled);
console.log('Parent enabled:', this.entity.parent ? this.entity.parent.enabled : 'no parent');
```

#### D. 摄像机视角问题
- 球体可能在移动，但摄像机角度看不到
- 尝试调整摄像机位置或角度

## 🔍 可能的根本原因

1. **物理系统配置问题**: PlayCanvas物理系统可能没有正确初始化
2. **坐标系问题**: 力的方向可能与预期不符
3. **时间步长问题**: 物理更新频率可能有问题
4. **约束冲突**: 可能存在隐藏的物理约束
5. **渲染问题**: 物理计算正常但渲染没有更新

## 📝 建议的测试方法

### 方法1: 手动设置位置
```javascript
// 在控制台中执行
var sphere = pc.app.root.findByName('可移动球体');
sphere.setPosition(2, 5, 0);
```

### 方法2: 检查物理世界
```javascript
// 检查物理世界中的刚体数量
console.log('Physics bodies:', pc.app.systems.rigidbody._bodies.length);
```

### 方法3: 强制物理更新
```javascript
// 强制物理系统更新
pc.app.systems.rigidbody.update(0.016);
```

## 🎮 临时解决方案

如果物理系统有问题，可以使用直接位置操作作为临时方案：

```javascript
// 临时跳跃实现
SphereMovement.prototype.handleJumpDirect = function() {
    if (this.keyboard.isPressed(pc.KEY_SPACE) && this.jumpCooldown <= 0) {
        var pos = this.entity.getPosition();
        this.entity.setPosition(pos.x, pos.y + 2, pos.z);
        this.jumpCooldown = 0.5;
        console.log('直接位置跳跃');
    }
};
```

请运行场景并查看控制台输出，然后告诉我看到了什么信息！
