/**
 * 交错木地板生成器
 * 生成真实的木地板交错拼接效果，替换规整的瓷砖地板
 */

var StaggeredFloorGenerator = pc.createScript('staggeredFloorGenerator');

// 脚本属性
StaggeredFloorGenerator.attributes.add('floorWidth', {
    type: 'number',
    default: 16,
    title: '地板宽度',
    description: '地板的总宽度（X方向）'
});

StaggeredFloorGenerator.attributes.add('floorDepth', {
    type: 'number',
    default: 16,
    title: '地板深度',
    description: '地板的总深度（Z方向）'
});

StaggeredFloorGenerator.attributes.add('boardWidth', {
    type: 'number',
    default: 0.8,
    title: '木板宽度',
    description: '单块木板的宽度'
});

StaggeredFloorGenerator.attributes.add('boardLength', {
    type: 'number',
    default: 4.0,
    title: '木板长度',
    description: '单块木板的长度'
});

StaggeredFloorGenerator.attributes.add('boardThickness', {
    type: 'number',
    default: 0.1,
    title: '木板厚度',
    description: '单块木板的厚度'
});

StaggeredFloorGenerator.attributes.add('staggerOffset', {
    type: 'number',
    default: 0.5,
    title: '交错偏移',
    description: '交错排列的偏移比例（0-1）'
});

StaggeredFloorGenerator.attributes.add('gapSize', {
    type: 'number',
    default: 0.02,
    title: '缝隙大小',
    description: '木板之间的缝隙大小'
});

StaggeredFloorGenerator.attributes.add('materialAsset', {
    type: 'asset',
    assetType: 'material',
    title: '木板材质',
    description: '木板使用的材质资源'
});

StaggeredFloorGenerator.attributes.add('autoGenerate', {
    type: 'boolean',
    default: true,
    title: '自动生成',
    description: '脚本启动时自动生成地板'
});

// 初始化
StaggeredFloorGenerator.prototype.initialize = function() {
    console.log('=== 交错木地板生成器初始化 ===');
    
    // 存储生成的木板实体
    this.floorBoards = [];
    
    if (this.autoGenerate) {
        // 延迟生成，确保场景完全加载
        setTimeout(() => {
            this.generateStaggeredFloor();
        }, 100);
    }
};

// 生成交错木地板
StaggeredFloorGenerator.prototype.generateStaggeredFloor = function() {
    console.log('开始生成交错木地板...');
    
    // 清除现有地板
    this.clearExistingFloor();
    
    // 计算需要的行数和列数
    const rowCount = Math.ceil(this.floorDepth / this.boardWidth);
    const baseColCount = Math.ceil(this.floorLength / this.boardLength);
    
    // 生成交错排列的木板
    for (let row = 0; row < rowCount; row++) {
        this.generateFloorRow(row, baseColCount);
    }
    
    console.log(`地板生成完成！共生成 ${this.floorBoards.length} 块木板`);
};

// 生成一行木板
StaggeredFloorGenerator.prototype.generateFloorRow = function(rowIndex, baseColCount) {
    const isEvenRow = rowIndex % 2 === 0;
    const offsetX = isEvenRow ? 0 : this.boardLength * this.staggerOffset;
    
    // 计算这一行需要的木板数量
    const colCount = isEvenRow ? baseColCount : baseColCount + 1;
    
    for (let col = 0; col < colCount; col++) {
        this.createFloorBoard(rowIndex, col, offsetX);
    }
};

// 创建单块木板
StaggeredFloorGenerator.prototype.createFloorBoard = function(row, col, offsetX) {
    // 计算位置
    const x = (col * this.boardLength) + offsetX - (this.floorWidth / 2);
    const z = (row * (this.boardWidth + this.gapSize)) - (this.floorDepth / 2);
    const y = 0;
    
    // 检查是否超出边界
    if (x + this.boardLength/2 > this.floorWidth/2 || x - this.boardLength/2 < -this.floorWidth/2) {
        return; // 超出边界，不生成
    }
    
    // 创建木板实体
    const boardEntity = new pc.Entity(`FloorBoard_${row}_${col}`);
    
    // 添加渲染组件
    boardEntity.addComponent('render', {
        type: 'box',
        material: this.materialAsset ? this.materialAsset.resource : null
    });
    
    // 添加碰撞组件
    boardEntity.addComponent('collision', {
        type: 'box',
        halfExtents: [this.boardLength/2, this.boardThickness/2, this.boardWidth/2]
    });
    
    // 添加刚体组件（静态）
    boardEntity.addComponent('rigidbody', {
        type: 'static'
    });
    
    // 设置位置和缩放
    boardEntity.setPosition(x, y, z);
    boardEntity.setLocalScale(this.boardLength, this.boardThickness, this.boardWidth);
    
    // 添加到场景
    this.app.root.addChild(boardEntity);
    this.floorBoards.push(boardEntity);
    
    // 添加标签以便识别
    boardEntity.tags.add('floor');
    boardEntity.tags.add('wood-board');
};

// 清除现有地板
StaggeredFloorGenerator.prototype.clearExistingFloor = function() {
    console.log('清除现有地板...');
    
    // 清除之前生成的木板
    this.floorBoards.forEach(board => {
        if (board && board.destroy) {
            board.destroy();
        }
    });
    this.floorBoards = [];
    
    // 查找并删除现有的地板实体
    const existingFloors = this.app.root.findByTag('floor');
    existingFloors.forEach(floor => {
        if (floor && floor.destroy) {
            floor.destroy();
        }
    });
    
    // 查找并删除名为"Plane"的地面实体
    const planeEntity = this.app.root.findByName('Plane');
    if (planeEntity) {
        console.log('删除现有平面地面');
        planeEntity.destroy();
    }
};

// 重新生成地板（公共方法）
StaggeredFloorGenerator.prototype.regenerateFloor = function() {
    console.log('重新生成地板...');
    this.generateStaggeredFloor();
};

// 获取地板信息
StaggeredFloorGenerator.prototype.getFloorInfo = function() {
    return {
        boardCount: this.floorBoards.length,
        floorSize: { width: this.floorWidth, depth: this.floorDepth },
        boardSize: { 
            length: this.boardLength, 
            width: this.boardWidth, 
            thickness: this.boardThickness 
        }
    };
};

// 脚本销毁时清理
StaggeredFloorGenerator.prototype.destroy = function() {
    console.log('交错木地板生成器销毁');
    // 注意：不在这里删除木板，因为它们应该保留在场景中
};
