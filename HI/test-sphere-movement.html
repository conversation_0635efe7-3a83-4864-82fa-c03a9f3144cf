<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>球体移动测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .control-item {
            margin: 5px 0;
            display: flex;
            align-items: center;
        }
        .key {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 3px;
            font-weight: bold;
            margin-right: 10px;
            min-width: 30px;
            text-align: center;
        }
        .description {
            color: #666;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            background: #e9ecef;
            border-radius: 5px;
            font-family: monospace;
        }
        .iframe-container {
            width: 100%;
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 PlayCanvas 球体移动控制测试</h1>
        
        <div class="controls">
            <h3>控制说明：</h3>
            <div class="control-item">
                <span class="key">W</span>
                <span class="description">向前移动</span>
            </div>
            <div class="control-item">
                <span class="key">S</span>
                <span class="description">向后移动</span>
            </div>
            <div class="control-item">
                <span class="key">A</span>
                <span class="description">向左移动</span>
            </div>
            <div class="control-item">
                <span class="key">D</span>
                <span class="description">向右移动</span>
            </div>
            <div class="control-item">
                <span class="key">空格</span>
                <span class="description">跳跃</span>
            </div>
            <div class="control-item">
                <span class="key">↑↓←→</span>
                <span class="description">方向键也可以控制移动</span>
            </div>
        </div>

        <div class="status">
            <strong>脚本功能：</strong><br>
            ✅ WASD键控制水平移动<br>
            ✅ 空格键跳跃<br>
            ✅ 地面检测防止无限跳跃<br>
            ✅ 物理引擎驱动的真实运动<br>
            ✅ 可调节的移动力度和跳跃力度<br>
        </div>

        <h3>PlayCanvas 场景：</h3>
        <div class="iframe-container">
            <p style="text-align: center; padding: 50px; color: #666;">
                请在PlayCanvas编辑器中运行场景来测试球体移动功能。<br>
                确保球体实体已经添加了sphereMovement脚本。
            </p>
        </div>

        <div style="margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 5px;">
            <h4>📝 使用说明：</h4>
            <ol>
                <li>确保球体实体有以下组件：
                    <ul>
                        <li>Render Component (type: sphere)</li>
                        <li>Collision Component (type: sphere)</li>
                        <li>RigidBody Component (type: dynamic)</li>
                        <li>Script Component (sphereMovement脚本)</li>
                    </ul>
                </li>
                <li>在PlayCanvas编辑器中点击"Launch"按钮运行场景</li>
                <li>使用WASD键或方向键控制球体移动</li>
                <li>按空格键让球体跳跃</li>
                <li>可以在脚本属性中调整移动力度和跳跃力度</li>
            </ol>
        </div>
    </div>
</body>
</html>
