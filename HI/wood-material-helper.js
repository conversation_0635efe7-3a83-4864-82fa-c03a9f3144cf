/**
 * 木板材质助手脚本
 * 自动创建和配置木板材质，提供多种预设效果
 */

var WoodMaterialHelper = pc.createScript('woodMaterialHelper');

// 脚本属性
WoodMaterialHelper.attributes.add('materialName', {
    type: 'string',
    default: 'WoodFloorMaterial',
    title: '材质名称',
    description: '创建的材质名称'
});

WoodMaterialHelper.attributes.add('woodType', {
    type: 'string',
    enum: [
        { 'Oak': 'oak' },
        { 'Pine': 'pine' },
        { 'Walnut': 'walnut' },
        { 'Maple': 'maple' },
        { 'Cherry': 'cherry' }
    ],
    default: 'oak',
    title: '木材类型',
    description: '选择木材类型预设'
});

WoodMaterialHelper.attributes.add('finish', {
    type: 'string',
    enum: [
        { 'Matte': 'matte' },
        { 'Satin': 'satin' },
        { 'Gloss': 'gloss' }
    ],
    default: 'satin',
    title: '表面处理',
    description: '木板表面光泽度'
});

WoodMaterialHelper.attributes.add('autoCreate', {
    type: 'boolean',
    default: true,
    title: '自动创建',
    description: '脚本启动时自动创建材质'
});

// 木材颜色预设
WoodMaterialHelper.prototype.woodColors = {
    oak: { r: 0.65, g: 0.45, b: 0.25 },      // 橡木 - 中等棕色
    pine: { r: 0.85, g: 0.75, b: 0.55 },     // 松木 - 浅黄棕色
    walnut: { r: 0.35, g: 0.25, b: 0.15 },   // 胡桃木 - 深棕色
    maple: { r: 0.95, g: 0.85, b: 0.65 },    // 枫木 - 浅色
    cherry: { r: 0.75, g: 0.35, b: 0.25 }    // 樱桃木 - 红棕色
};

// 表面处理预设
WoodMaterialHelper.prototype.finishSettings = {
    matte: { shininess: 5, metalness: 0.0 },
    satin: { shininess: 25, metalness: 0.1 },
    gloss: { shininess: 60, metalness: 0.2 }
};

// 初始化
WoodMaterialHelper.prototype.initialize = function() {
    console.log('=== 木板材质助手初始化 ===');
    
    if (this.autoCreate) {
        setTimeout(() => {
            this.createWoodMaterial();
        }, 100);
    }
};

// 创建木板材质
WoodMaterialHelper.prototype.createWoodMaterial = function() {
    console.log(`创建木板材质: ${this.materialName} (${this.woodType}, ${this.finish})`);
    
    // 检查是否已存在同名材质
    const existingMaterial = this.app.assets.findByName(this.materialName);
    if (existingMaterial.length > 0) {
        console.log('材质已存在，将更新现有材质');
        this.updateMaterial(existingMaterial[0]);
        return existingMaterial[0];
    }
    
    // 创建新材质
    const material = new pc.StandardMaterial();
    material.name = this.materialName;
    
    // 应用木材颜色
    const woodColor = this.woodColors[this.woodType];
    material.diffuse.set(woodColor.r, woodColor.g, woodColor.b);
    
    // 应用表面处理
    const finish = this.finishSettings[this.finish];
    material.shininess = finish.shininess;
    material.metalness = finish.metalness;
    
    // 设置其他属性
    material.useMetalness = true;
    material.useLighting = true;
    
    // 添加细微的环境光反射
    material.ambient.set(0.2, 0.2, 0.2);
    
    // 创建材质资源
    const materialAsset = new pc.Asset(this.materialName, 'material', null, material);
    this.app.assets.add(materialAsset);
    
    console.log('木板材质创建完成:', this.materialName);
    return materialAsset;
};

// 更新现有材质
WoodMaterialHelper.prototype.updateMaterial = function(materialAsset) {
    const material = materialAsset.resource;
    
    // 应用木材颜色
    const woodColor = this.woodColors[this.woodType];
    material.diffuse.set(woodColor.r, woodColor.g, woodColor.b);
    
    // 应用表面处理
    const finish = this.finishSettings[this.finish];
    material.shininess = finish.shininess;
    material.metalness = finish.metalness;
    
    // 更新材质
    material.update();
    
    console.log('材质更新完成:', this.materialName);
};

// 获取创建的材质
WoodMaterialHelper.prototype.getMaterial = function() {
    const materials = this.app.assets.findByName(this.materialName);
    return materials.length > 0 ? materials[0] : null;
};

// 应用材质到地板生成器
WoodMaterialHelper.prototype.applyToFloorGenerator = function() {
    const material = this.getMaterial();
    if (!material) {
        console.error('未找到材质:', this.materialName);
        return false;
    }
    
    // 查找地板生成器
    const floorGenerator = this.app.root.findByName('FloorGenerator');
    if (!floorGenerator || !floorGenerator.script.staggeredFloorGenerator) {
        console.error('未找到地板生成器');
        return false;
    }
    
    // 设置材质
    floorGenerator.script.staggeredFloorGenerator.materialAsset = material;
    
    // 重新生成地板以应用新材质
    floorGenerator.script.staggeredFloorGenerator.regenerateFloor();
    
    console.log('材质已应用到地板生成器');
    return true;
};

// 创建材质变体
WoodMaterialHelper.prototype.createMaterialVariant = function(variantName, colorModifier) {
    const baseMaterial = this.getMaterial();
    if (!baseMaterial) {
        console.error('基础材质不存在');
        return null;
    }
    
    // 复制基础材质
    const newMaterial = baseMaterial.resource.clone();
    newMaterial.name = variantName;
    
    // 应用颜色修改
    if (colorModifier) {
        newMaterial.diffuse.mul(new pc.Color(colorModifier.r, colorModifier.g, colorModifier.b));
    }
    
    // 创建新的材质资源
    const newMaterialAsset = new pc.Asset(variantName, 'material', null, newMaterial);
    this.app.assets.add(newMaterialAsset);
    
    console.log('材质变体创建完成:', variantName);
    return newMaterialAsset;
};

// 预设材质快速创建
WoodMaterialHelper.prototype.createPresetMaterials = function() {
    console.log('创建预设材质集合...');
    
    const presets = [
        { name: 'LightOak', type: 'oak', finish: 'satin' },
        { name: 'DarkWalnut', type: 'walnut', finish: 'gloss' },
        { name: 'NaturalPine', type: 'pine', finish: 'matte' },
        { name: 'RichCherry', type: 'cherry', finish: 'satin' },
        { name: 'PaleMaple', type: 'maple', finish: 'matte' }
    ];
    
    const createdMaterials = [];
    
    presets.forEach(preset => {
        // 临时修改属性
        const originalType = this.woodType;
        const originalFinish = this.finish;
        const originalName = this.materialName;
        
        this.woodType = preset.type;
        this.finish = preset.finish;
        this.materialName = preset.name;
        
        // 创建材质
        const material = this.createWoodMaterial();
        createdMaterials.push(material);
        
        // 恢复原始属性
        this.woodType = originalType;
        this.finish = originalFinish;
        this.materialName = originalName;
    });
    
    console.log(`预设材质创建完成，共 ${createdMaterials.length} 个材质`);
    return createdMaterials;
};

// 获取所有木板材质
WoodMaterialHelper.prototype.getAllWoodMaterials = function() {
    const allMaterials = this.app.assets.filter(asset => {
        return asset.type === 'material' && 
               asset.name.toLowerCase().includes('wood');
    });
    
    return allMaterials;
};

// 脚本销毁
WoodMaterialHelper.prototype.destroy = function() {
    console.log('木板材质助手销毁');
};
