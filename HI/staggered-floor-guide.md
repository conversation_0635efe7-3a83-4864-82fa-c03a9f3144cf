# 🏠 PlayCanvas 交错木地板生成器使用指南

## 📋 概述

这个脚本可以将规整的瓷砖地板替换为真实的交错木地板效果，模拟真实木地板的拼接模式。

## ✨ 效果对比

### 🔴 替换前（规整瓷砖）
- 正方形瓷砖整齐排列
- 网格状布局
- 缺乏真实感

### 🟢 替换后（交错木地板）
- 长条形木板
- 交错拼接模式
- 真实木地板效果
- 可调节的缝隙和偏移

## 🚀 快速开始

### 步骤1: 上传脚本
1. 在PlayCanvas编辑器中，进入 **Assets** 面板
2. 右键点击 → **Upload** → 选择 `staggered-floor-generator.js`
3. 等待脚本上传完成

### 步骤2: 创建地板生成器实体
1. 在 **Hierarchy** 面板中右键 → **New Entity**
2. 重命名为 "FloorGenerator"
3. 选中该实体，在 **Inspector** 面板中点击 **Add Component** → **Script**
4. 在Script组件中点击 **Add Script** → 选择 "staggeredFloorGenerator"

### 步骤3: 配置参数
在Script组件中调整以下参数：

#### 🏗️ 地板尺寸
- **Floor Width**: `16` - 地板总宽度
- **Floor Depth**: `16` - 地板总深度

#### 📏 木板尺寸
- **Board Width**: `0.8` - 木板宽度（垂直于长度方向）
- **Board Length**: `4.0` - 木板长度
- **Board Thickness**: `0.1` - 木板厚度

#### 🔄 排列效果
- **Stagger Offset**: `0.5` - 交错偏移比例（0-1）
- **Gap Size**: `0.02` - 木板间缝隙大小

#### 🎨 外观
- **Material Asset**: 选择木板材质（可选）
- **Auto Generate**: `true` - 自动生成

### 步骤4: 运行生成
1. 确保 **Auto Generate** 为 `true`
2. 点击 **Launch** 运行场景
3. 脚本会自动删除现有地板并生成新的交错木地板

## 🎛️ 参数详解

### 地板尺寸参数
```javascript
floorWidth: 16    // X方向总宽度
floorDepth: 16    // Z方向总深度
```

### 木板尺寸参数
```javascript
boardWidth: 0.8   // 木板宽度（短边）
boardLength: 4.0  // 木板长度（长边）
boardThickness: 0.1 // 木板厚度（Y方向）
```

### 排列效果参数
```javascript
staggerOffset: 0.5  // 交错偏移比例
// 0.0 = 无偏移（对齐）
// 0.5 = 半个木板长度偏移（推荐）
// 1.0 = 完整木板长度偏移

gapSize: 0.02      // 木板间缝隙
// 0.0 = 无缝隙
// 0.02 = 小缝隙（推荐）
// 0.05 = 大缝隙
```

## 🎨 材质设置

### 创建木板材质
1. 在 **Assets** 面板右键 → **New Asset** → **Material**
2. 重命名为 "WoodFloorMaterial"
3. 在材质设置中：
   - **Diffuse**: 设置木纹颜色（如 #8B4513）
   - **Diffuse Map**: 上传木纹贴图（可选）
   - **Normal Map**: 上传法线贴图增加细节（可选）

### 应用材质
1. 选中FloorGenerator实体
2. 在Script组件中，将 **Material Asset** 设置为创建的材质

## 🔧 高级功能

### 手动重新生成
在控制台中执行：
```javascript
// 获取地板生成器
var generator = pc.app.root.findByName('FloorGenerator');
if (generator && generator.script.staggeredFloorGenerator) {
    // 重新生成地板
    generator.script.staggeredFloorGenerator.regenerateFloor();
}
```

### 获取地板信息
```javascript
var generator = pc.app.root.findByName('FloorGenerator');
var info = generator.script.staggeredFloorGenerator.getFloorInfo();
console.log('地板信息:', info);
```

### 动态调整参数
```javascript
var script = generator.script.staggeredFloorGenerator;
script.boardLength = 3.0;  // 修改木板长度
script.staggerOffset = 0.3; // 修改交错偏移
script.regenerateFloor();   // 重新生成
```

## 🎯 效果预设

### 经典木地板
```javascript
boardWidth: 0.8
boardLength: 4.0
staggerOffset: 0.5
gapSize: 0.02
```

### 宽板木地板
```javascript
boardWidth: 1.2
boardLength: 5.0
staggerOffset: 0.4
gapSize: 0.03
```

### 窄板木地板
```javascript
boardWidth: 0.6
boardLength: 3.0
staggerOffset: 0.6
gapSize: 0.015
```

## 🐛 故障排除

### 问题1: 地板没有生成
**解决方案**:
1. 检查控制台是否有错误信息
2. 确保 `autoGenerate` 为 `true`
3. 手动调用 `regenerateFloor()`

### 问题2: 木板太大或太小
**解决方案**:
1. 调整 `boardLength` 和 `boardWidth` 参数
2. 确保尺寸与场景比例匹配

### 问题3: 交错效果不明显
**解决方案**:
1. 增加 `staggerOffset` 值（推荐0.3-0.7）
2. 调整 `gapSize` 使缝隙更明显

### 问题4: 球体穿透地板
**解决方案**:
1. 检查木板的碰撞组件是否正确
2. 确保 `boardThickness` 足够厚（建议≥0.1）

## 📊 性能优化

### 大面积地板优化
- 适当增加 `boardLength` 减少木板数量
- 使用LOD系统（距离远时简化）
- 考虑分块生成大型地板

### 内存优化
- 重复使用相同的材质资源
- 避免过小的 `gapSize` 导致过多木板

## 🎮 与球体移动的兼容性

这个地板生成器完全兼容现有的球体移动系统：
- ✅ 自动添加碰撞检测
- ✅ 支持物理交互
- ✅ 保持地面检测功能
- ✅ 不影响移动和跳跃

## 🚀 下一步

生成交错木地板后，你可以：
1. 测试球体在新地板上的移动
2. 调整材质和光照效果
3. 添加更多场景元素
4. 优化性能和视觉效果

**现在就试试你的新木地板吧！** 🏠✨
